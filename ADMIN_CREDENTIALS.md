# Admin Credentials - Antosa Architect System

## Default Login Credentials

### Admin Account
- **Username:** `ian`
- **Password:** `admin1`
- **Level:** `admin`
- **Access:** Full admin dashboard and management features

### Proyek Account  
- **Username:** `lingga`
- **Password:** `proyek`
- **Level:** `proyek`
- **Access:** Project management features

## Database Information

### Table: `petugas`
Password di database sudah menggunakan hash yang aman dengan `password_hash()` function PHP.

### Security Notes
1. ✅ Password sudah di-hash menggunakan `PASSWORD_DEFAULT` algorithm
2. ✅ Menggunakan salt yang random untuk setiap password
3. ✅ Verifikasi password menggunakan `password_verify()` function

## Login Process
1. Buka halaman login: `index.php`
2. Masukkan username dan password sesuai di atas
3. Sistem akan memverifikasi menggunakan `password_verify()`
4. Redirect ke dashboard sesuai level user

## File Terkait
- `cek_login.php` - Proses autentikasi
- `buat_hash.php` - Generator hash password (untuk development)
- `arsitek.sql` - Database schema dan data default
- `includes/session_manager.php` - Session management

## Untuk Development
Jika perlu membuat password hash baru, jalankan:
```bash
php buat_hash.php
```

## Security Recommendations
1. Ganti password default setelah instalasi
2. Gunakan password yang kuat (minimal 8 karakter, kombinasi huruf, angka, simbol)
3. Aktifkan HTTPS di production
4. Regular backup database
5. Monitor login logs

---
*Last updated: 2025-07-14*
