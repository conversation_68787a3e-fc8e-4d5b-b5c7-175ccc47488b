<?php
// Password yang ingin di-hash
$password_admin = 'admin1';
$password_proyek = 'proyek';

// Membuat hash
$hash_untuk_admin = password_hash($password_admin, PASSWORD_DEFAULT);
$hash_untuk_proyek = password_hash($password_proyek, PASSWORD_DEFAULT);

echo "<h3>Password Hash Generator</h3>";
echo "<p><strong>Admin Credentials:</strong></p>";
echo "<ul>";
echo "<li>Username: ian</li>";
echo "<li>Password: admin1</li>";
echo "<li>Hash: " . $hash_untuk_admin . "</li>";
echo "</ul>";

echo "<hr>";

echo "<p><strong>Proyek Credentials:</strong></p>";
echo "<ul>";
echo "<li>Username: lingga</li>";
echo "<li>Password: proyek</li>";
echo "<li>Hash: " . $hash_untuk_proyek . "</li>";
echo "</ul>";

echo "<hr>";
echo "<p><em>Note: Hash akan berbeda setiap kali di-generate karena menggunakan salt yang random.</em></p>";
?>